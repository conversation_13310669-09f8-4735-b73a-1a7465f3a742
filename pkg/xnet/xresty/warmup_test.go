package xresty

import (
	"net/http"
	"sync"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/testing/xhttpmock"
)

func TestWarmup(t *testing.T) {
	// Setup test client and server
	uri := "http://example.com/"
	c := New().SetBaseURL(uri)

	xhttpmock.ActivateNonDefault(c.GetClient())
	// defer xhttpmock.DeactivateAndReset() // avoid race

	var requestCount int
	requestCountMutex := &sync.Mutex{}

	xhttpmock.RegisterResponder("GET", uri+"api/health",
		func(req *http.Request) (*http.Response, error) {
			requestCountMutex.Lock()
			requestCount++
			requestCountMutex.Unlock()
			return xhttpmock.NewStringResponse(200, "OK"), nil
		},
	)

	Convey("Warmup", t, func() {
		Convey("When calling Warmup with valid URL", func() {
			// Use a very short interval for testing
			err := Warmup(c, uri+"api/health",
				WarmupParallel(1),
				WarmupJitter(10*time.Millisecond),
				WarmupInterval(50*time.Millisecond))

			Convey("Then it should not return an error", func() {
				So(err, ShouldBeNil)

				// Wait a bit to allow some requests to be made
				time.Sleep(200 * time.Millisecond)

				// Check that at least one request was made
				requestCountMutex.Lock()
				count := requestCount
				requestCountMutex.Unlock()

				So(count, ShouldBeGreaterThan, 0)
			})
		})

		Convey("When calling Warmup with invalid URL", func() {
			err := Warmup(c, ":\\invalid")

			Convey("Then it should return an error", func() {
				So(err, ShouldNotBeNil)
			})
		})

		Convey("When calling Warmup multiple times with same client and host", func() {
			// Reset counter
			requestCountMutex.Lock()
			requestCount = 0
			requestCountMutex.Unlock()

			err1 := Warmup(c, uri+"api/health",
				WarmupParallel(1),
				WarmupJitter(10*time.Millisecond),
				WarmupInterval(50*time.Millisecond))

			// Second call with same client and host
			err2 := Warmup(c, uri+"api/health")

			Convey("Then both calls should succeed", func() {
				So(err1, ShouldBeNil)
				So(err2, ShouldBeNil)

				// Wait a bit to allow some requests to be made
				time.Sleep(200 * time.Millisecond)

				// Check that requests were made
				requestCountMutex.Lock()
				count := requestCount
				requestCountMutex.Unlock()

				// Only one goroutine should be started due to LoadOrStore
				So(count, ShouldBeGreaterThan, 0)
			})
		})
	})
}

func TestWarmupOptions(t *testing.T) {
	Convey("WarmupOptions", t, func() {
		Convey("When creating new WarmupOptions with default values", func() {
			opts := newWarmupOptions()

			Convey("Then it should have correct default values", func() {
				So(opts.Parallel, ShouldBeGreaterThan, 0)
				So(opts.Jitter, ShouldEqual, 10*time.Second)
				So(opts.Interval, ShouldEqual, 15*time.Second)
			})
		})

		Convey("When creating new WarmupOptions with custom values", func() {
			opts := newWarmupOptions(
				WarmupParallel(5),
				WarmupJitter(20*time.Second),
				WarmupInterval(30*time.Second),
			)

			Convey("Then it should have the custom values", func() {
				So(opts.Parallel, ShouldEqual, 5)
				So(opts.Jitter, ShouldEqual, 20*time.Second)
				So(opts.Interval, ShouldEqual, 30*time.Second)
			})
		})

		Convey("When using WarmupParallel option", func() {
			option := WarmupParallel(10)
			opts := &WarmupOptions{}
			option(opts)

			Convey("Then it should set the Parallel field", func() {
				So(opts.Parallel, ShouldEqual, 10)
			})
		})

		Convey("When using WarmupJitter option", func() {
			option := WarmupJitter(5 * time.Second)
			opts := &WarmupOptions{}
			option(opts)

			Convey("Then it should set the Jitter field", func() {
				So(opts.Jitter, ShouldEqual, 5*time.Second)
			})
		})

		Convey("When using WarmupInterval option", func() {
			option := WarmupInterval(25 * time.Second)
			opts := &WarmupOptions{}
			option(opts)

			Convey("Then it should set the Interval field", func() {
				So(opts.Interval, ShouldEqual, 25*time.Second)
			})
		})
	})
}

func TestWarmupKey(t *testing.T) {
	Convey("warmupKey", t, func() {
		Convey("When creating two warmupKey instances with same client and URL", func() {
			c := New()
			key1 := warmupKey{c, "example.com"}
			key2 := warmupKey{c, "example.com"}

			Convey("Then they should be equal for map operations", func() {
				m := make(map[warmupKey]bool)
				m[key1] = true
				So(m[key2], ShouldBeTrue)
			})
		})

		Convey("When creating two warmupKey instances with different URLs", func() {
			c := New()
			key1 := warmupKey{c, "example.com"}
			key2 := warmupKey{c, "other.com"}

			Convey("Then they should be different for map operations", func() {
				m := make(map[warmupKey]bool)
				m[key1] = true
				So(m[key2], ShouldBeFalse)
			})
		})

		Convey("When creating two warmupKey instances with different clients", func() {
			c1 := New()
			c2 := New()
			key1 := warmupKey{c1, "example.com"}
			key2 := warmupKey{c2, "example.com"}

			Convey("Then they should be different for map operations", func() {
				m := make(map[warmupKey]bool)
				m[key1] = true
				So(m[key2], ShouldBeFalse)
			})
		})
	})
}

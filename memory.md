# Sparrow Memory Cache 系统深度分析

## 系统概述

Sparrow Memory Cache 是一个高性能的内存缓存系统，提供了两种核心缓存实现：LRU（最近最少使用）缓存和TTL（生存时间）缓存。系统采用线程安全设计，支持多种缓存策略，适用于高并发场景下的本地缓存需求。

## 核心架构

### 1. 系统分层架构

```mermaid
graph TB
    subgraph "Application Layer"
        A[Business Logic]
        B[Cache Operations]
        C[Data Access]
    end
    
    subgraph "Cache Strategy Layer"
        D[LRU Cache]
        E[TTL Cache]
        F[Cache Interface]
    end
    
    subgraph "Data Structure Layer"
        G[Doubly Linked List]
        H[Hash Map]
        I[Timer System]
    end
    
    subgraph "Concurrency Layer"
        J[Mutex Locks]
        K[RWMutex Locks]
        L[Thread Safety]
    end
    
    subgraph "Memory Management"
        M[Garbage Collection]
        N[Memory Pool]
        O[Eviction Policies]
    end
    
    A --> B
    B --> D
    B --> E
    D --> F
    E --> F
    
    D --> G
    D --> H
    E --> H
    E --> I
    
    D --> J
    E --> K
    J --> L
    K --> L
    
    G --> M
    H --> N
    D --> O
    E --> O
```

### 2. 核心组件关系图

```mermaid
classDiagram
    class LRUCache {
        -cap int
        -ll DoublyLinkedList
        -cache map[any]Element
        -mu Mutex
        -OnEvicted Function
        +Add(key, val)
        +Get(key) any
        +Remove(key)
        +Down(key)
        +Len() int
        +Clear()
    }
    
    class TTLCache {
        -cache map[any]TTLEntry
        -mu RWMutex
        +Get(key) any
        +Set(key, val, ttl, fn)
        +SetNX(key, val, ttl, fn)
        +Remove(key) any
        +Clear()
    }
    
    class LRUEntry {
        +key any
        +val any
    }
    
    class TTLEntry {
        +value any
        +fn Function
        +timer Timer
        +deleted bool
    }
    
    class DoublyLinkedList {
        +PushFront(val) Element
        +MoveToFront(elem)
        +MoveToBack(elem)
        +Remove(elem)
        +Back() Element
        +Len() int
    }
    
    class Timer {
        +AfterFunc(duration, fn) Timer
        +Stop() bool
    }
    
    LRUCache --> LRUEntry
    LRUCache --> DoublyLinkedList
    TTLCache --> TTLEntry
    TTLEntry --> Timer
```

## 核心流程分析

### 3. LRU 缓存操作流程

```mermaid
sequenceDiagram
    participant App as Application
    participant LRU as LRUCache
    participant List as LinkedList
    participant Map as HashMap
    
    App->>LRU: Add(key, value)
    LRU->>LRU: Lock mutex
    LRU->>Map: Check if key exists
    
    alt Key Exists
        Map-->>LRU: Return element
        LRU->>List: MoveToFront(element)
        LRU->>LRU: Update value
    else Key Not Exists
        LRU->>List: PushFront(new entry)
        List-->>LRU: Return new element
        LRU->>Map: Store key->element mapping
        
        alt Cache Full
            LRU->>List: Get back element
            LRU->>LRU: removeElement(back)
            LRU->>Map: Delete old key
            LRU->>LRU: Call OnEvicted callback
        end
    end
    
    LRU->>LRU: Unlock mutex
    LRU-->>App: Operation complete
```

### 4. TTL 缓存操作流程

```mermaid
sequenceDiagram
    participant App as Application
    participant TTL as TTLCache
    participant Timer as TimerSystem
    participant Map as HashMap
    
    App->>TTL: Set(key, value, ttl, callback)
    TTL->>TTL: Lock mutex
    TTL->>Map: Check if key exists
    
    alt Key Exists
        TTL->>Timer: Stop old timer
        TTL->>TTL: Call old callback if needed
    end
    
    TTL->>Timer: Create new timer with TTL
    Timer-->>TTL: Return timer instance
    TTL->>Map: Store new entry
    TTL->>TTL: Unlock mutex
    TTL-->>App: Operation complete
    
    Note over Timer: Timer expires after TTL
    Timer->>TTL: Timer callback triggered
    TTL->>TTL: Lock mutex
    TTL->>Map: Remove expired entry
    TTL->>TTL: Unlock mutex
    TTL->>App: Call expiration callback
```

### 5. LRU 访问模式流程

```mermaid
flowchart TD
    A[Get Request] --> B{Key in Cache?}
    B -->|Yes| C[Move to Front]
    B -->|No| D[Return nil]
    
    C --> E[Return Value]
    
    F[Add Request] --> G{Key Exists?}
    G -->|Yes| H[Update Value & Move to Front]
    G -->|No| I[Add to Front]
    
    I --> J{Cache Full?}
    J -->|Yes| K[Remove LRU Item]
    J -->|No| L[Complete Add]
    
    K --> M[Call OnEvicted]
    M --> L
    
    H --> L
    E --> N[Access Complete]
    D --> N
    L --> N
```

## 关键设计模式

### 6. LRU 算法实现

```mermaid
graph LR
    subgraph "LRU Data Structure"
        A[Hash Map]
        B[Doubly Linked List]
        C[Head Pointer]
        D[Tail Pointer]
    end
    
    subgraph "Access Pattern"
        E[Most Recent]
        F[Least Recent]
        G[O1 Access]
        H[O1 Eviction]
    end
    
    A --> G
    B --> H
    C --> E
    D --> F
    
    E --> I[Front of List]
    F --> J[Back of List]
```

### 7. TTL 过期机制

```mermaid
stateDiagram-v2
    [*] --> Active
    Active --> Expired: Timer Fires
    Active --> Removed: Manual Remove
    Active --> Updated: Set Same Key
    
    state Active {
        [*] --> Waiting
        Waiting --> Accessed: Get Operation
        Accessed --> Waiting
    }
    
    Expired --> Cleanup: Call Callback
    Removed --> Cleanup: Call Callback
    Updated --> Cleanup: Stop Old Timer
    
    Cleanup --> [*]
```

### 8. 并发安全设计

```mermaid
graph TB
    subgraph "LRU Concurrency"
        A[Mutex Lock]
        B[Exclusive Access]
        C[Simple Locking]
    end
    
    subgraph "TTL Concurrency"
        D[RWMutex Lock]
        E[Read/Write Separation]
        F[Optimized Reads]
    end
    
    subgraph "Safety Guarantees"
        G[Data Race Prevention]
        H[Atomic Operations]
        I[Consistent State]
    end
    
    A --> B
    B --> C
    D --> E
    E --> F
    
    C --> G
    F --> H
    G --> I
    H --> I
```

## 技术实现细节

### 9. 内存管理策略

| 缓存类型 | 内存管理 | 特点 |
|----------|----------|------|
| LRU Cache | 容量限制 | 固定大小，自动淘汰 |
| TTL Cache | 时间限制 | 自动过期，无容量限制 |

### 10. 性能特性对比

| 操作 | LRU Cache | TTL Cache | 时间复杂度 |
|------|-----------|-----------|------------|
| Get | O(1) | O(1) | 常数时间 |
| Set/Add | O(1) | O(1) | 常数时间 |
| Remove | O(1) | O(1) | 常数时间 |
| 过期处理 | 手动 | 自动 | - |

### 11. 线程安全机制

```mermaid
flowchart TD
    A[Concurrent Access] --> B{Cache Type}
    
    B -->|LRU| C[Mutex Lock]
    B -->|TTL| D[RWMutex Lock]
    
    C --> E[Exclusive Access]
    D --> F{Operation Type}
    
    F -->|Read| G[Read Lock]
    F -->|Write| H[Write Lock]
    
    E --> I[Thread Safe]
    G --> I
    H --> I
```

## 使用示例

### 12. LRU 缓存使用

```go
// 创建容量为100的LRU缓存
cache := memory.NewLRUCache(100)

// 设置淘汰回调
cache.OnEvicted = func(key, value any) {
    log.Printf("Evicted: %v -> %v", key, value)
}

// 添加数据
cache.Add("user:1", &User{ID: 1, Name: "Alice"})
cache.Add("user:2", &User{ID: 2, Name: "Bob"})

// 获取数据
if value, ok := cache.Get("user:1"); ok {
    user := value.(*User)
    fmt.Printf("Found user: %s", user.Name)
}

// 手动移除
cache.Remove("user:2")

// 降低优先级
cache.Down("user:1")

// 清空缓存
cache.Clear()
```

### 13. TTL 缓存使用

```go
// 创建TTL缓存
cache := memory.NewTTLCache()

// 设置带过期时间的数据
cache.Set("session:abc123", sessionData, 30*time.Minute, func() {
    log.Println("Session expired")
})

// 设置不存在时才添加
cache.SetNX("lock:resource", "locked", 5*time.Second, func() {
    log.Println("Lock released")
})

// 获取数据
if value := cache.Get("session:abc123"); value != nil {
    session := value.(*SessionData)
    // 使用session数据
}

// 手动移除
cache.Remove("session:abc123")

// 清空所有缓存
cache.Clear()
```

### 14. 高级使用模式

```go
// 组合使用LRU和TTL
type HybridCache struct {
    lru *memory.LRUCache
    ttl *memory.TTLCache
}

func NewHybridCache(capacity int) *HybridCache {
    return &HybridCache{
        lru: memory.NewLRUCache(capacity),
        ttl: memory.NewTTLCache(),
    }
}

func (h *HybridCache) Set(key, value any, ttl time.Duration) {
    // 同时存储到LRU和TTL缓存
    h.lru.Add(key, value)
    h.ttl.Set(key, value, ttl, func() {
        h.lru.Remove(key) // TTL过期时从LRU中移除
    })
}

func (h *HybridCache) Get(key any) any {
    // 优先从LRU获取（更快）
    if value, ok := h.lru.Get(key); ok {
        return value
    }
    // 回退到TTL缓存
    return h.ttl.Get(key)
}
```

## 使用场景

### 15. 典型应用场景

1. **Web 应用缓存**
   - 用户会话缓存（TTL）
   - 页面数据缓存（LRU）
   - API 响应缓存（TTL）

2. **数据库查询缓存**
   - 热点数据缓存（LRU）
   - 临时查询结果（TTL）
   - 计算结果缓存（LRU）

3. **微服务架构**
   - 服务间调用缓存（TTL）
   - 配置数据缓存（LRU）
   - 认证令牌缓存（TTL）

4. **实时系统**
   - 实时数据缓存（TTL）
   - 计算状态缓存（LRU）
   - 临时存储（TTL）

### 16. 性能优化建议

```mermaid
graph TB
    subgraph "LRU 优化"
        A[合理设置容量]
        B[监控命中率]
        C[优化淘汰策略]
    end
    
    subgraph "TTL 优化"
        D[合理设置过期时间]
        E[避免频繁创建Timer]
        F[批量清理过期数据]
    end
    
    subgraph "通用优化"
        G[减少锁竞争]
        H[预分配内存]
        I[监控内存使用]
    end
    
    A --> G
    B --> H
    C --> I
    D --> G
    E --> H
    F --> I
```

### 17. 部署架构示例

```mermaid
graph TB
    subgraph "Application Instances"
        A1[App Instance 1]
        A2[App Instance 2]
        A3[App Instance 3]
    end
    
    subgraph "Local Memory Cache"
        L1[LRU + TTL Cache 1]
        L2[LRU + TTL Cache 2]
        L3[LRU + TTL Cache 3]
    end
    
    subgraph "Shared Resources"
        D[Database]
        R[Redis Cluster]
        M[Message Queue]
    end
    
    A1 --> L1
    A2 --> L2
    A3 --> L3
    
    L1 -.-> D
    L2 -.-> D
    L3 -.-> D
    
    L1 -.-> R
    L2 -.-> R
    L3 -.-> R
    
    A1 --> M
    A2 --> M
    A3 --> M
```

## 监控与调试

### 18. 关键监控指标

| 指标类型 | LRU Cache | TTL Cache |
|----------|-----------|-----------|
| 命中率 | Get成功/总Get请求 | Get非nil/总Get请求 |
| 内存使用 | 当前大小/最大容量 | 当前条目数 |
| 淘汰频率 | OnEvicted调用次数 | Timer过期次数 |
| 并发性能 | 锁等待时间 | 读写锁竞争 |

### 19. 调试和故障排查

```go
// LRU缓存调试
func (c *LRUCache) Debug() map[string]interface{} {
    c.mu.Lock()
    defer c.mu.Unlock()
    
    return map[string]interface{}{
        "capacity": c.cap,
        "current_size": c.ll.Len(),
        "utilization": float64(c.ll.Len()) / float64(c.cap),
    }
}

// TTL缓存调试
func (c *TTLCache) Debug() map[string]interface{} {
    c.mu.RLock()
    defer c.mu.RUnlock()
    
    activeTimers := 0
    for _, entry := range c.cache {
        if !entry.deleted {
            activeTimers++
        }
    }
    
    return map[string]interface{}{
        "total_entries": len(c.cache),
        "active_timers": activeTimers,
    }
}
```

## 总结

Sparrow Memory Cache 系统是一个设计精良的本地缓存解决方案，具有以下优势：

1. **双重策略**: LRU和TTL两种缓存策略，满足不同场景需求
2. **线程安全**: 完善的并发控制机制，支持高并发访问
3. **高性能**: O(1)时间复杂度的核心操作，性能优异
4. **内存高效**: 智能的内存管理和垃圾回收机制
5. **易于使用**: 简洁的API设计，易于集成和使用
6. **可扩展**: 支持回调机制，便于扩展和监控

该系统适用于需要高性能本地缓存的各种场景，是构建高性能应用的重要基础组件。

# Sparrow Election System 深度架构分析

## 系统概述

Sparrow Election System 是一个基于分布式共识算法的领导者选举系统，主要用于在分布式环境中选举出唯一的领导者节点。该系统采用了插件化的提供者模式，支持多种后端存储（目前主要支持 etcd3）。

## 核心架构

### 1. 系统分层架构

```mermaid
graph TB
    subgraph "Application Layer"
        A[Election Client]
        B[Configuration]
        C[Metrics]
    end
    
    subgraph "Core Layer"
        D[Election Core]
        E[Provider Interface]
        F[Options & Config]
    end
    
    subgraph "Provider Layer"
        G[ETCD Provider]
        H[Provider Registry]
    end
    
    subgraph "Infrastructure Layer"
        I[ETCD Cluster]
        J[Prometheus Metrics]
        K[Logging System]
    end
    
    A --> D
    B --> F
    C --> J
    D --> E
    E --> G
    G --> I
    D --> K
    H --> G
```

### 2. 核心组件关系图

```mermaid
classDiagram
    class Election {
        -ctx Context
        -cancel Function
        -opts Options
        -logger Logger
        -wg WaitGroup
        -leader AtomicBool
        +New(options) Election
        +campaign()
        +setLeader(bool)
        +Leader() bool
        +Close()
    }

    class Provider {
        <<interface>>
        +Campaign(ctx, key) Channel
    }

    class Options {
        +Name string
        +Prefix string
        +Provider Provider
        +Notice Function
    }

    class Config {
        +Name string
        +Prefix string
        +Provider string
        +KeyPath string
        +Build(options) Election
    }

    class ETCDProvider {
        -client ETCDClient
        +Campaign(ctx, key) Channel
    }

    Election --> Options
    Election --> Provider
    Provider <|-- ETCDProvider
    Config --> Election
    Options --> Provider
```

## 核心流程分析

### 3. 选举流程图

```mermaid
sequenceDiagram
    participant C as Client
    participant E as Election
    participant P as Provider
    participant ETCD as ETCD Cluster
    
    C->>E: New(options...)
    E->>E: Initialize context & logger
    E->>E: Start campaign goroutine
    
    loop Campaign Loop
        E->>P: Campaign(ctx, key)
        P->>ETCD: Create session with TTL=15s
        P->>ETCD: NewElection(session, key)
        P->>ETCD: Campaign(ctx, internal_ip)
        
        alt Campaign Success
            ETCD-->>P: Return resign channel
            P-->>E: Return resign channel
            E->>E: setLeader(true)
            E->>E: Notify observers
            E->>E: Update metrics
            
            alt Context Done
                E->>E: setLeader(false)
                E->>E: Exit campaign
            else Resign Signal
                ETCD-->>E: Resign channel closed
                E->>E: setLeader(false)
                E->>E: Continue campaign loop
            end
            
        else Campaign Failed
            P-->>E: Return error
            E->>E: setLeader(false)
            E->>E: Sleep 1 second
            E->>E: Continue campaign loop
        end
    end
```

### 4. 状态转换图

```mermaid
stateDiagram-v2
    [*] --> Initializing
    Initializing --> Campaigning: Start campaign
    
    state Campaigning {
        [*] --> Attempting
        Attempting --> Leading: Campaign success
        Attempting --> Waiting: Campaign failed
        Waiting --> Attempting: Retry after 1s
        Leading --> Attempting: Resign/Session expired
    }
    
    Campaigning --> Stopped: Context cancelled
    Stopped --> [*]
    
    Leading --> NotifyObservers: State change
    NotifyObservers --> Leading
    
    Leading --> UpdateMetrics: State change
    UpdateMetrics --> Leading
```

## 关键设计模式

### 5. Provider 模式架构

```mermaid
graph LR
    subgraph "Provider Registry"
        A[providers map]
        B[RegisterProvider]
        C[NewProvider]
    end
    
    subgraph "Provider Interface"
        D[Provider Interface]
        E[Campaign Method]
    end
    
    subgraph "Concrete Providers"
        F[ETCD Provider]
        G[Future Provider 1]
        H[Future Provider 2]
    end
    
    B --> A
    C --> A
    A --> D
    D --> E
    E --> F
    E --> G
    E --> H
```

### 6. 配置系统架构

```mermaid
graph TB
    subgraph "Configuration Sources"
        A[Default Config]
        B[File Config]
        C[Environment Variables]
    end
    
    subgraph "Configuration Processing"
        D[xconf.UnmarshalKey]
        E[Config Validation]
        F[Provider Creation]
    end
    
    subgraph "Election Creation"
        G[Options Building]
        H[Election Instance]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
```

## 技术实现细节

### 7. 并发安全设计

系统采用了多种并发安全机制：

1. **原子操作**: 使用 `atomic.Bool` 管理领导者状态
2. **Context 控制**: 使用 context 进行优雅关闭
3. **WaitGroup 同步**: 确保 goroutine 正确退出
4. **Channel 通信**: 使用 channel 进行状态通知

### 8. 错误处理与重试机制

```mermaid
flowchart TD
    A[Campaign Start] --> B{Campaign Success?}
    B -->|Yes| C[Become Leader]
    B -->|No| D{Context Done?}
    D -->|Yes| E[Exit Campaign]
    D -->|No| F[Log Error]
    F --> G[Sleep 1 Second]
    G --> A
    
    C --> H{Wait for Signal}
    H -->|Resign| I[Lose Leadership]
    H -->|Context Done| E
    I --> A
```

### 9. 监控与可观测性

系统集成了完整的监控体系：

- **Metrics**: 使用 Prometheus 指标监控选举状态
- **Logging**: 结构化日志记录关键事件
- **Tracing**: 请求 ID 追踪

```mermaid
graph LR
    subgraph "Observability"
        A[Election Events]
        B[Metrics Collection]
        C[Log Aggregation]
        D[Alerting]
    end
    
    A --> B
    A --> C
    B --> D
    C --> D
```

## 性能特性

### 10. 关键性能参数

| 参数 | 值 | 说明 |
|------|----|----|
| Session TTL | 15秒 | ETCD 会话超时时间 |
| Retry Interval | 1秒 | 选举失败重试间隔 |
| Dial Timeout | 5秒 | ETCD 连接超时 |

### 11. 扩展性设计

系统具有良好的扩展性：

1. **Provider 插件化**: 支持多种后端存储
2. **配置灵活性**: 支持多种配置源
3. **观察者模式**: 支持状态变化通知
4. **指标可定制**: 支持自定义监控指标

## 使用场景

### 12. 典型应用场景

1. **分布式任务调度**: 确保只有一个节点执行定时任务
2. **数据库主从切换**: 选举数据库主节点
3. **缓存更新协调**: 协调分布式缓存更新
4. **配置管理**: 选举配置管理节点

### 13. 部署架构示例

```mermaid
graph TB
    subgraph "Application Cluster"
        A1[App Instance 1]
        A2[App Instance 2]
        A3[App Instance 3]
    end
    
    subgraph "ETCD Cluster"
        E1[ETCD Node 1]
        E2[ETCD Node 2]
        E3[ETCD Node 3]
    end
    
    subgraph "Monitoring"
        M1[Prometheus]
        M2[Grafana]
        M3[AlertManager]
    end
    
    A1 --> E1
    A2 --> E2
    A3 --> E3
    
    A1 --> M1
    A2 --> M1
    A3 --> M1
    
    M1 --> M2
    M1 --> M3
```

## 总结

Sparrow Election System 是一个设计精良的分布式选举系统，具有以下优势：

1. **高可用性**: 基于 ETCD 的强一致性保证
2. **易扩展性**: 插件化的 Provider 架构
3. **可观测性**: 完整的监控和日志体系
4. **并发安全**: 多重并发安全保障机制
5. **配置灵活**: 支持多种配置方式

该系统适用于需要分布式协调和领导者选举的各种场景，是构建分布式系统的重要基础组件。
